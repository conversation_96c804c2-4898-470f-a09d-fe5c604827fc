#!/usr/bin/env python3
"""
飞书文件查看器启动脚本
"""
import os
import sys

def main():
    print("🚀 启动飞书文件查看器...")
    
    # 检查数据文件是否存在
    if not os.path.exists("feishu_files.json"):
        print("❌ 未找到数据文件 feishu_files.json")
        print("请先运行 main.py 进行文件扫描")
        
        response = input("是否现在运行扫描程序？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            try:
                import subprocess
                subprocess.run([sys.executable, "main.py"])
            except Exception as e:
                print(f"启动扫描程序失败: {e}")
                return
        else:
            return
    
    # 启动文件查看器
    try:
        from file_viewer import FeishuFileViewer
        print("✅ 正在启动文件查看器...")
        app = FeishuFileViewer()
        app.run()
    except ImportError as e:
        print(f"❌ 导入文件查看器失败: {e}")
        print("请确保 file_viewer.py 文件存在")
    except Exception as e:
        print(f"❌ 启动文件查看器失败: {e}")

if __name__ == "__main__":
    main()
