"""
安装脚本 - 自动安装依赖和设置环境
"""
import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"正在{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    print("🚀 飞书云文档扫描工具 - 环境设置")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 安装Python依赖
    if not run_command("pip install -r requirements.txt", "安装Python依赖"):
        print("尝试使用pip3...")
        if not run_command("pip3 install -r requirements.txt", "安装Python依赖"):
            print("❌ 无法安装Python依赖，请手动运行: pip install -r requirements.txt")
            sys.exit(1)
    
    # 安装Playwright浏览器
    if not run_command("playwright install chromium", "安装Playwright浏览器"):
        print("❌ 无法安装Playwright浏览器，请手动运行: playwright install chromium")
        sys.exit(1)
    
    print("\n🎉 环境设置完成！")
    print("\n📋 使用说明:")
    print("1. 运行主程序: python main.py")
    print("2. 运行测试: python test_feishu.py")
    print("3. 查看README.md了解更多信息")
    
    # 询问是否立即运行测试
    response = input("\n是否现在运行测试？(y/n): ").lower().strip()
    if response in ['y', 'yes', '是']:
        print("\n正在运行测试...")
        os.system("python test_feishu.py")

if __name__ == "__main__":
    main()
