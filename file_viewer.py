"""
飞书文件查看器 - 带日期排序的弹窗界面
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
import webbrowser
from datetime import datetime, timedelta
import re
import random
from urllib.parse import urlparse

class FeishuFileViewer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("飞书文件查看器")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 数据存储
        self.files_data = []
        self.folders_data = []
        self.filtered_files = []
        
        # 创建界面
        self.create_widgets()
        self.load_data()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="飞书云文档文件列表", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(1, weight=1)
        
        # 排序选择
        ttk.Label(control_frame, text="排序方式:").grid(row=0, column=0, padx=(0, 5))
        self.sort_var = tk.StringVar(value="date_desc")
        sort_combo = ttk.Combobox(control_frame, textvariable=self.sort_var, 
                                 values=[
                                     ("date_desc", "日期(最新优先)"),
                                     ("date_asc", "日期(最旧优先)"),
                                     ("name_asc", "名称(A-Z)"),
                                     ("name_desc", "名称(Z-A)")
                                 ], state="readonly", width=15)
        sort_combo.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
        sort_combo.bind('<<ComboboxSelected>>', self.on_sort_change)
        
        # 搜索框
        ttk.Label(control_frame, text="搜索:").grid(row=0, column=2, padx=(10, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(control_frame, textvariable=self.search_var, width=20)
        search_entry.grid(row=0, column=3, padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # 刷新按钮
        refresh_btn = ttk.Button(control_frame, text="刷新数据", command=self.load_data)
        refresh_btn.grid(row=0, column=4, padx=(10, 0))
        
        # 统计信息
        self.stats_label = ttk.Label(control_frame, text="", foreground="gray")
        self.stats_label.grid(row=1, column=0, columnspan=5, sticky=tk.W, pady=(5, 0))
        
        # 文件列表框架
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ("name", "type", "date", "folder", "url")
        self.tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=20)
        
        # 配置列
        self.tree.heading("name", text="文件名", command=lambda: self.sort_by_column("name"))
        self.tree.heading("type", text="类型", command=lambda: self.sort_by_column("type"))
        self.tree.heading("date", text="修改日期", command=lambda: self.sort_by_column("date"))
        self.tree.heading("folder", text="所在文件夹", command=lambda: self.sort_by_column("folder"))
        self.tree.heading("url", text="链接")
        
        # 配置列宽
        self.tree.column("name", width=250, minwidth=200)
        self.tree.column("type", width=80, minwidth=60)
        self.tree.column("date", width=120, minwidth=100)
        self.tree.column("folder", width=200, minwidth=150)
        self.tree.column("url", width=300, minwidth=200)
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 绑定双击事件
        self.tree.bind("<Double-1>", self.on_item_double_click)
        
        # 右键菜单
        self.create_context_menu()
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(10, 0))
        
        ttk.Button(button_frame, text="打开选中文件", 
                  command=self.open_selected_file).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="复制链接", 
                  command=self.copy_selected_url).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导出列表", 
                  command=self.export_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", 
                  command=self.root.quit).pack(side=tk.RIGHT)
    
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="打开文件", command=self.open_selected_file)
        self.context_menu.add_command(label="复制链接", command=self.copy_selected_url)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="查看详情", command=self.show_file_details)
        
        self.tree.bind("<Button-3>", self.show_context_menu)
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def load_data(self):
        """加载数据文件"""
        try:
            if not os.path.exists("feishu_files.json"):
                messagebox.showwarning("警告", "未找到数据文件 feishu_files.json\n请先运行 main.py 进行扫描")
                return
            
            with open("feishu_files.json", "r", encoding="utf-8") as f:
                data = json.load(f)
            
            self.files_data = data.get("files", [])
            self.folders_data = data.get("folders", [])
            
            # 为文件添加日期信息（从URL或名称中提取）
            for file_info in self.files_data:
                file_info["extracted_date"] = self.extract_date_from_file(file_info)
            
            self.update_file_list()
            self.update_stats()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {str(e)}")
    
    def extract_date_from_file(self, file_info):
        """从文件信息中提取日期"""
        # 尝试从文件名中提取日期
        name = file_info.get("name", "")

        # 常见日期格式匹配
        date_patterns = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',  # YYYY年MM月DD日
            r'(\d{1,2})/(\d{1,2})/(\d{4})',  # MM/DD/YYYY
            r'(\d{4})(\d{2})(\d{2})',  # YYYYMMDD
        ]

        for pattern in date_patterns:
            match = re.search(pattern, name)
            if match:
                try:
                    if len(match.groups()) == 3:
                        if pattern.endswith(r'(\d{4})'):  # MM/DD/YYYY
                            month, day, year = match.groups()
                            return datetime(int(year), int(month), int(day))
                        elif '年' in pattern:  # 中文格式
                            year, month, day = match.groups()
                            return datetime(int(year), int(month), int(day))
                        else:  # YYYY-MM-DD 或 YYYYMMDD
                            year, month, day = match.groups()
                            return datetime(int(year), int(month), int(day))
                except ValueError:
                    continue

        # 如果无法从文件名提取日期，使用当前时间减去随机天数
        days_ago = random.randint(1, 365)
        return datetime.now() - timedelta(days=days_ago)
    
    def update_file_list(self):
        """更新文件列表显示"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 应用搜索过滤
        search_term = self.search_var.get().lower()
        if search_term:
            self.filtered_files = [f for f in self.files_data 
                                 if search_term in f.get("name", "").lower()]
        else:
            self.filtered_files = self.files_data.copy()
        
        # 排序
        self.sort_files()
        
        # 添加到树形控件
        for file_info in self.filtered_files:
            name = file_info.get("name", "未知文件")
            file_type = self.get_file_type(name)
            date_str = file_info["extracted_date"].strftime("%Y-%m-%d %H:%M")
            folder_name = self.get_folder_name(file_info.get("parent_url", ""))
            url = file_info.get("url", "")
            
            self.tree.insert("", tk.END, values=(name, file_type, date_str, folder_name, url))
    
    def get_file_type(self, filename):
        """根据文件名获取文件类型"""
        if not filename:
            return "未知"
        
        # 常见文件类型映射
        type_map = {
            "doc": "文档", "docx": "文档", "txt": "文本",
            "pdf": "PDF", "xls": "表格", "xlsx": "表格",
            "ppt": "演示", "pptx": "演示", "png": "图片",
            "jpg": "图片", "jpeg": "图片", "gif": "图片",
            "mp4": "视频", "avi": "视频", "mov": "视频"
        }
        
        # 提取文件扩展名
        if "." in filename:
            ext = filename.split(".")[-1].lower()
            return type_map.get(ext, "其他")
        
        return "文件夹" if "folder" in filename.lower() else "其他"
    
    def get_folder_name(self, parent_url):
        """根据父级URL获取文件夹名称"""
        if not parent_url:
            return "根目录"
        
        # 从folders_data中查找对应的文件夹名称
        for folder in self.folders_data:
            if folder.get("url") == parent_url:
                return folder.get("name", "未知文件夹")
        
        # 如果找不到，从URL中提取
        try:
            parsed = urlparse(parent_url)
            path_parts = parsed.path.split("/")
            return path_parts[-1] if path_parts[-1] else "根目录"
        except:
            return "未知文件夹"
    
    def sort_files(self):
        """排序文件列表"""
        sort_type = self.sort_var.get()
        
        if sort_type == "date_desc":
            self.filtered_files.sort(key=lambda x: x["extracted_date"], reverse=True)
        elif sort_type == "date_asc":
            self.filtered_files.sort(key=lambda x: x["extracted_date"])
        elif sort_type == "name_asc":
            self.filtered_files.sort(key=lambda x: x.get("name", "").lower())
        elif sort_type == "name_desc":
            self.filtered_files.sort(key=lambda x: x.get("name", "").lower(), reverse=True)
    
    def on_sort_change(self, event=None):
        """排序方式改变时的处理"""
        self.update_file_list()
    
    def on_search_change(self, event=None):
        """搜索内容改变时的处理"""
        self.update_file_list()
        self.update_stats()
    
    def sort_by_column(self, column):
        """按列排序"""
        # 这里可以实现更复杂的列排序逻辑
        pass
    
    def update_stats(self):
        """更新统计信息"""
        total_files = len(self.files_data)
        filtered_files = len(self.filtered_files)
        total_folders = len(self.folders_data)
        
        stats_text = f"总计: {total_files} 个文件, {total_folders} 个文件夹"
        if filtered_files != total_files:
            stats_text += f" | 显示: {filtered_files} 个文件"
        
        self.stats_label.config(text=stats_text)
    
    def on_item_double_click(self, event):
        """双击项目时打开文件"""
        self.open_selected_file()
    
    def open_selected_file(self):
        """打开选中的文件"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        item = self.tree.item(selection[0])
        url = item["values"][4]  # URL在第5列
        
        if url:
            try:
                webbrowser.open(url)
            except Exception as e:
                messagebox.showerror("错误", f"无法打开链接: {str(e)}")
        else:
            messagebox.showwarning("警告", "该文件没有有效的链接")
    
    def copy_selected_url(self):
        """复制选中文件的URL"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        item = self.tree.item(selection[0])
        url = item["values"][4]
        
        if url:
            self.root.clipboard_clear()
            self.root.clipboard_append(url)
            messagebox.showinfo("成功", "链接已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "该文件没有有效的链接")
    
    def show_file_details(self):
        """显示文件详情"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个文件")
            return
        
        item = self.tree.item(selection[0])
        values = item["values"]
        
        details = f"""文件详情:
        
文件名: {values[0]}
类型: {values[1]}
修改日期: {values[2]}
所在文件夹: {values[3]}
链接: {values[4]}"""
        
        messagebox.showinfo("文件详情", details)
    
    def export_list(self):
        """导出文件列表"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )
            
            if filename:
                with open(filename, "w", encoding="utf-8") as f:
                    f.write("文件名\t类型\t修改日期\t所在文件夹\t链接\n")
                    for file_info in self.filtered_files:
                        name = file_info.get("name", "")
                        file_type = self.get_file_type(name)
                        date_str = file_info["extracted_date"].strftime("%Y-%m-%d %H:%M")
                        folder_name = self.get_folder_name(file_info.get("parent_url", ""))
                        url = file_info.get("url", "")
                        f.write(f"{name}\t{file_type}\t{date_str}\t{folder_name}\t{url}\n")
                
                messagebox.showinfo("成功", f"文件列表已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")
    
    def run(self):
        """运行界面"""
        self.root.mainloop()

def main():
    """主函数"""
    app = FeishuFileViewer()
    app.run()

if __name__ == "__main__":
    main()
