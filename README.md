# 飞书云文档扫描工具

这个工具可以帮助您扫描飞书云文档的文件夹结构，获取所有文件和文件夹的链接信息。

## 功能特性

- 🔐 支持自动登录状态保存和恢复
- 📁 递归扫描文件夹结构
- 📄 获取文件和文件夹的详细信息
- 💾 结果保存为JSON格式
- 🔄 支持断点续传（避免重复扫描）
- 🛡️ 防止被检测为自动化工具
- 🖥️ **图形化文件查看器**
  - 📅 按日期排序（最新文件优先）
  - 🔍 实时搜索过滤
  - 📋 文件列表导出
  - 🌐 一键打开文件链接
  - 📊 统计信息显示

## 快速开始

### 方法一：自动安装（推荐）
```bash
python setup.py
```

### 方法二：手动安装
```bash
pip install -r requirements.txt
playwright install chromium
```

## 环境要求

- Python 3.8+
- Chrome浏览器（推荐）
- 稳定的网络连接

## 使用方法

### 1. 运行测试（可选）
```bash
python test_feishu.py
```

### 2. 运行主程序
```bash
python main.py
```

### 3. 登录流程
- 首次运行时，会打开浏览器窗口
- 在浏览器中完成飞书登录（支持扫码或账号密码）
- 登录状态会自动保存，下次运行无需重新登录

### 4. 扫描过程
- 登录成功后，工具会自动开始扫描文件夹结构
- 控制台会显示扫描进度和发现的文件/文件夹
- 扫描完成后，结果会保存在 `feishu_files.json` 文件中

### 5. 查看结果

#### 方法一：使用图形化查看器（推荐）
```bash
python start_viewer.py
```
或者在Windows上双击 `run.bat` 选择选项4

#### 方法二：直接查看JSON文件
扫描结果保存在 `feishu_files.json` 中，包含：
- 文件夹名称和链接
- 文件名称和链接
- 父级文件夹关系
- 文件类型标识

## 文件查看器功能

图形化查看器提供以下功能：

### 📅 排序功能
- **日期排序**：最新文件优先显示
- **名称排序**：按字母顺序排列
- **自定义排序**：点击列标题进行排序

### 🔍 搜索功能
- 实时搜索文件名
- 支持模糊匹配
- 搜索结果高亮显示

### 🖱️ 操作功能
- **双击文件**：直接在浏览器中打开
- **右键菜单**：复制链接、查看详情
- **批量操作**：导出文件列表

### 📊 统计信息
- 显示文件和文件夹总数
- 实时更新过滤结果数量

## 输出文件

- `feishu_auth.json`: 保存的登录状态（下次运行时自动使用）
- `feishu_files.json`: 扫描结果，包含所有文件和文件夹信息

## 配置说明

可以在 `main.py` 中修改以下配置：

- `LOGIN_TIMEOUT`: 登录超时时间（默认5分钟）
- `PAGE_TIMEOUT`: 页面加载超时时间（默认30秒）
- `root_url`: 要扫描的根文件夹URL

## 注意事项

- 首次运行需要手动登录
- 建议在网络稳定的环境下运行
- 大量文件夹可能需要较长时间扫描
- 请遵守飞书的使用条款和API限制

## 故障排除

如果遇到登录问题：
1. 删除 `feishu_auth.json` 文件重新登录
2. 检查网络连接
3. 确认飞书账号有访问权限

如果扫描不完整：
1. 检查控制台日志信息
2. 可能需要调整选择器以适应页面结构变化
3. 确认目标文件夹的访问权限
