"""
测试脚本 - 验证 Playwright 和基本功能
"""
from playwright.sync_api import sync_playwright
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s: %(message)s')

def test_browser_launch():
    """测试浏览器启动功能"""
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(
                channel="chrome",
                headless=False,
                args=["--disable-extensions"]
            )
            context = browser.new_context()
            page = context.new_page()
            
            # 测试访问百度
            page.goto("https://www.baidu.com")
            title = page.title()
            logging.info(f"页面标题: {title}")
            
            # 等待3秒让用户看到页面
            page.wait_for_timeout(3000)
            
            browser.close()
            logging.info("浏览器测试成功！")
            return True
            
    except Exception as e:
        logging.error(f"浏览器测试失败: {e}")
        return False

def test_feishu_access():
    """测试飞书页面访问"""
    try:
        with sync_playwright() as p:
            browser = p.chromium.launch(
                channel="chrome", 
                headless=False,
                args=["--disable-extensions"]
            )
            context = browser.new_context()
            page = context.new_page()
            
            # 测试访问飞书登录页面
            feishu_url = "https://www.feishu.cn"
            page.goto(feishu_url)
            title = page.title()
            logging.info(f"飞书页面标题: {title}")
            
            # 等待5秒让用户看到页面
            page.wait_for_timeout(5000)
            
            browser.close()
            logging.info("飞书页面访问测试成功！")
            return True
            
    except Exception as e:
        logging.error(f"飞书页面访问测试失败: {e}")
        return False

if __name__ == "__main__":
    logging.info("开始运行测试...")
    
    # 测试浏览器启动
    if test_browser_launch():
        logging.info("✅ 浏览器启动测试通过")
    else:
        logging.error("❌ 浏览器启动测试失败")
        exit(1)
    
    # 测试飞书页面访问
    if test_feishu_access():
        logging.info("✅ 飞书页面访问测试通过")
    else:
        logging.error("❌ 飞书页面访问测试失败")
    
    logging.info("所有测试完成！")
