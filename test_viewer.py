#!/usr/bin/env python3
"""
文件查看器测试脚本
使用示例数据测试查看器功能
"""
import os
import shutil
import json

def setup_test_data():
    """设置测试数据"""
    print("🔧 正在设置测试数据...")
    
    # 备份现有数据（如果存在）
    if os.path.exists("feishu_files.json"):
        print("📦 备份现有数据文件...")
        shutil.copy("feishu_files.json", "feishu_files.json.backup")
    
    # 复制示例数据
    if os.path.exists("sample_data.json"):
        shutil.copy("sample_data.json", "feishu_files.json")
        print("✅ 测试数据设置完成")
        return True
    else:
        print("❌ 未找到示例数据文件 sample_data.json")
        return False

def restore_data():
    """恢复原始数据"""
    print("🔄 正在恢复原始数据...")
    
    if os.path.exists("feishu_files.json.backup"):
        shutil.copy("feishu_files.json.backup", "feishu_files.json")
        os.remove("feishu_files.json.backup")
        print("✅ 原始数据已恢复")
    else:
        # 如果没有备份，删除测试数据
        if os.path.exists("feishu_files.json"):
            os.remove("feishu_files.json")
        print("✅ 测试数据已清理")

def run_viewer():
    """运行文件查看器"""
    try:
        from file_viewer import FeishuFileViewer
        print("🚀 启动文件查看器...")
        app = FeishuFileViewer()
        app.run()
    except ImportError as e:
        print(f"❌ 导入文件查看器失败: {e}")
    except Exception as e:
        print(f"❌ 运行文件查看器失败: {e}")

def main():
    print("🧪 飞书文件查看器测试工具")
    print("=" * 40)
    
    print("\n选择操作:")
    print("1. 使用测试数据运行查看器")
    print("2. 恢复原始数据")
    print("3. 查看测试数据内容")
    print("4. 退出")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        if setup_test_data():
            run_viewer()
            
            # 询问是否恢复数据
            response = input("\n测试完成，是否恢复原始数据？(y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                restore_data()
    
    elif choice == "2":
        restore_data()
    
    elif choice == "3":
        if os.path.exists("sample_data.json"):
            with open("sample_data.json", "r", encoding="utf-8") as f:
                data = json.load(f)
            
            print(f"\n📊 测试数据统计:")
            print(f"文件夹数量: {len(data.get('folders', []))}")
            print(f"文件数量: {len(data.get('files', []))}")
            
            print(f"\n📁 文件夹列表:")
            for folder in data.get('folders', []):
                print(f"  - {folder.get('name', '未知')}")
            
            print(f"\n📄 文件列表:")
            for file in data.get('files', []):
                print(f"  - {file.get('name', '未知')}")
        else:
            print("❌ 未找到测试数据文件")
    
    elif choice == "4":
        print("👋 再见！")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
