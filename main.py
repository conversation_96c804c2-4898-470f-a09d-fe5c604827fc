from playwright.sync_api import sync_playwright, TimeoutError
from collections import deque
import logging
import json
import os
import time
from urllib.parse import urljoin

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s: %(message)s')

# 配置常量
AUTH_FILE = "feishu_auth.json"
RESULTS_FILE = "feishu_files.json"
LOGIN_TIMEOUT = 300000  # 5分钟登录超时
PAGE_TIMEOUT = 30000    # 30秒页面加载超时

def load_auth_state():
    """加载保存的登录状态"""
    if os.path.exists(AUTH_FILE):
        try:
            with open(AUTH_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.warning(f"加载登录状态失败: {e}")
    return None

def save_auth_state(context):
    """保存登录状态"""
    try:
        auth_state = context.storage_state()
        with open(AUTH_FILE, 'w', encoding='utf-8') as f:
            json.dump(auth_state, f, ensure_ascii=False, indent=2)
        logging.info("登录状态已保存")
    except Exception as e:
        logging.error(f"保存登录状态失败: {e}")

def check_login_status(page):
    """检查是否已登录"""
    try:
        # 检查是否存在用户头像或用户信息
        selectors_to_check = [
            "[data-testid='user-avatar']",
            ".user-avatar",
            "[class*='avatar']",
            "[class*='user']",
            ".lark-avatar"
        ]

        for selector in selectors_to_check:
            if page.query_selector(selector):
                logging.info(f"检测到登录状态，选择器: {selector}")
                return True

        # 检查URL是否包含登录页面特征
        current_url = page.url
        if "login" in current_url.lower() or "passport" in current_url.lower():
            return False

        return False
    except Exception as e:
        logging.error(f"检查登录状态时出错: {e}")
        return False

def wait_for_login(page):
    """等待用户登录"""
    logging.info("请在浏览器中完成登录...")
    logging.info("支持扫码登录或账号密码登录")

    start_time = time.time()
    while time.time() - start_time < LOGIN_TIMEOUT / 1000:
        try:
            if check_login_status(page):
                logging.info("登录成功！")
                return True
            time.sleep(2)  # 每2秒检查一次
        except Exception as e:
            logging.error(f"等待登录时出错: {e}")
            time.sleep(5)

    logging.error("登录超时")
    return False

def scan_folders(context, root_url):
    """迭代遍历文件夹结构（避免递归栈溢出）"""
    visited = set()  # 记录已访问的URL
    queue = deque([root_url])  # 使用队列代替递归
    results = {
        "folders": [],
        "files": []
    }

    while queue:
        current_url = queue.popleft()
        if current_url in visited:
            continue

        try:
            # 为每个文件夹创建独立页面（避免状态污染）
            with context.new_page() as page:
                logging.info(f"正在扫描: {current_url}")

                # 优化等待机制
                page.goto(current_url, wait_until="domcontentloaded", timeout=PAGE_TIMEOUT)

                # 等待页面内容加载
                time.sleep(3)

                # 多种选择器尝试，适应飞书的不同页面结构
                folder_selectors = [
                    "[data-testid*='folder'] a",
                    ".folder-item a",
                    "[class*='folder'] a",
                    "[data-node-type='folder'] a",
                    "div[role='button'][class*='folder']",
                    ".lark-tree-node[data-type='folder'] a"
                ]

                file_selectors = [
                    "[data-testid*='file'] a",
                    ".file-item a",
                    "[class*='file'] a",
                    "[data-node-type='file'] a",
                    "div[role='button'][class*='file']",
                    ".lark-tree-node[data-type='file'] a"
                ]

                # 尝试获取文件夹元素
                folder_elements = []
                for selector in folder_selectors:
                    elements = page.query_selector_all(selector)
                    if elements:
                        folder_elements.extend(elements)
                        logging.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个文件夹")
                        break

                # 处理文件夹
                for folder in folder_elements:
                    try:
                        folder_url = folder.get_attribute("href")
                        folder_text = folder.inner_text().strip()

                        if folder_url and folder_url not in visited:
                            # 确保URL是完整的
                            if folder_url.startswith('/'):
                                folder_url = urljoin(current_url, folder_url)

                            folder_info = {
                                "name": folder_text,
                                "url": folder_url,
                                "type": "folder",
                                "parent_url": current_url
                            }
                            results["folders"].append(folder_info)
                            queue.append(folder_url)
                            logging.info(f"发现文件夹: {folder_text} -> {folder_url}")
                    except Exception as e:
                        logging.warning(f"处理文件夹元素时出错: {e}")

                # 尝试获取文件元素
                file_elements = []
                for selector in file_selectors:
                    elements = page.query_selector_all(selector)
                    if elements:
                        file_elements.extend(elements)
                        logging.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个文件")
                        break

                # 处理文件
                for file in file_elements:
                    try:
                        file_url = file.get_attribute("href")
                        file_text = file.inner_text().strip()

                        if file_url:
                            # 确保URL是完整的
                            if file_url.startswith('/'):
                                file_url = urljoin(current_url, file_url)

                            file_info = {
                                "name": file_text,
                                "url": file_url,
                                "type": "file",
                                "parent_url": current_url
                            }
                            results["files"].append(file_info)
                            logging.info(f"发现文件: {file_text} -> {file_url}")
                    except Exception as e:
                        logging.warning(f"处理文件元素时出错: {e}")

                visited.add(current_url)

                # 如果没有找到任何内容，尝试其他方法
                if not folder_elements and not file_elements:
                    logging.warning(f"在 {current_url} 中未找到文件或文件夹，尝试其他方法...")
                    # 可以在这里添加更多的选择器或处理逻辑

        except TimeoutError:
            logging.warning(f"页面加载超时: {current_url}")
        except Exception as e:
            logging.error(f"处理 {current_url} 时出错: {str(e)}")

    return results

def save_results(results):
    """保存扫描结果到文件"""
    try:
        with open(RESULTS_FILE, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logging.info(f"扫描结果已保存到 {RESULTS_FILE}")

        # 打印统计信息
        logging.info(f"扫描完成！共发现 {len(results['folders'])} 个文件夹，{len(results['files'])} 个文件")
    except Exception as e:
        logging.error(f"保存结果失败: {e}")

def main():
    with sync_playwright() as p:
        # 启动浏览器（增加超时设置）
        browser = p.chromium.launch(
            channel="chrome",
            headless=False,
            args=[
                "--disable-extensions",  # 禁用所有浏览器插件
                "--disable-blink-features=AutomationControlled",  # 避免被检测为自动化
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ]
        )

        # 加载之前保存的登录状态
        auth_state = load_auth_state()
        if auth_state:
            context = browser.new_context(storage_state=auth_state)
            logging.info("已加载保存的登录状态")
        else:
            context = browser.new_context()
            logging.info("未找到保存的登录状态，需要重新登录")

        logging.info("浏览器已启动，准备进行文件夹扫描...")

        try:
            # 创建页面并尝试访问目标URL
            page = context.new_page()
            root_url = "https://my-ichery.feishu.cn/drive/folder/MOrgf9AHDljVGfdSo6dcJottn9b"

            logging.info(f"正在访问: {root_url}")
            page.goto(root_url, wait_until="domcontentloaded", timeout=PAGE_TIMEOUT)

            # 等待页面加载
            time.sleep(20)

            # 检查是否需要登录
            if not check_login_status(page):
                logging.info("需要登录，请在浏览器中完成登录...")
                if not wait_for_login(page):
                    logging.error("登录失败或超时")
                    return

                # 保存登录状态
                save_auth_state(context)
            else:
                logging.info("已检测到登录状态")

            # 开始扫描
            logging.info("开始扫描文件夹结构...")
            results = scan_folders(context, root_url)

            # 保存结果
            save_results(results)

            # 保持浏览器打开一段时间，让用户查看结果
            logging.info("扫描完成，浏览器将在10秒后关闭...")
            time.sleep(10)

        except Exception as e:
            logging.error(f"执行过程中出错: {e}")
        finally:
            browser.close()

if __name__ == "__main__":
    main()