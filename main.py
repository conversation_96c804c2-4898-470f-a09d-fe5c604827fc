from playwright.sync_api import sync_playwright, TimeoutError
from collections import deque
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s: %(message)s')

def scan_folders(context, root_url):
    """迭代遍历文件夹结构（避免递归栈溢出）"""
    visited = set()  # 记录已访问的URL
    queue = deque([root_url])  # 使用队列代替递归
    
    while queue:
        current_url = queue.popleft()
        if current_url in visited:
            continue
            
        try:
            # 为每个文件夹创建独立页面（避免状态污染）
            with context.new_page() as page:
                # 优化等待机制
                page.goto(current_url, wait_until="domcontentloaded", timeout=15000)
                page.wait_for_selector(".folder-item, .file-item", state="attached", timeout=10000)
                
                # 获取文件夹元素
                folder_elements = page.query_selector_all(".folder-item a")
                for folder in folder_elements:
                    folder_url = folder.get_attribute("href")
                    if folder_url and folder_url not in visited:
                        logging.info(f"发现文件夹: {folder_url}")
                        queue.append(folder_url)
                
                # 获取文件元素
                file_elements = page.query_selector_all(".file-item a")
                for file in file_elements:
                    file_url = file.get_attribute("href")
                    if file_url:
                        logging.info(f"发现文件: {file_url}")
                
                visited.add(current_url)
        
        except TimeoutError:
            logging.warning(f"页面加载超时: {current_url}")
        except Exception as e:
            logging.error(f"处理 {current_url} 时出错: {str(e)}")

def main():
    with sync_playwright() as p:
        # 启动浏览器（增加超时设置）
       
        browser = p.chromium.launch(
                channel="chrome",
                headless=False,
                args=["--disable-extensions"]  # 禁用所有浏览器插件
            )
        context = browser.new_context()
        
        logging.info("浏览器已启动，准备进行文件夹扫描...")
        
        try:
            # 登录处理
            if not context.cookies():
                page = context.new_page()
                page.goto("https://my-ichery.feishu.cn/drive/folder/MOrgf9AHDljVGfdSo6dcJottn9b", wait_until="networkidle")
                logging.info("请扫码登录飞书账号...")
                page.sleep(120000)
                #page.wait_for_selector(".user-avatar", state="attached", timeout=150000)  # 2分钟登录超时
                #context.storage_state(path="auth.json")  # 保存登录状态
                logging.info("登录成功！")
            
            # 开始扫描
            root_url = "https://my-ichery.feishu.cn/drive/folder/MOrgf9AHDljVGfdSo6dcJottn9b"
            scan_folders(context, root_url)
            
        finally:
            browser.close()

if __name__ == "__main__":
    main()