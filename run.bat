@echo off
chcp 65001 >nul
echo 🚀 飞书云文档扫描工具
echo ========================

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 选择操作:
echo 1. 安装/更新依赖
echo 2. 运行测试
echo 3. 运行主程序(扫描文件)
echo 4. 打开文件查看器
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5):

if "%choice%"=="1" (
    echo 正在安装依赖...
    python setup.py
    pause
) else if "%choice%"=="2" (
    echo 正在运行测试...
    python test_feishu.py
    pause
) else if "%choice%"=="3" (
    echo 正在启动飞书扫描工具...
    python main.py
    pause
) else if "%choice%"=="4" (
    echo 正在启动文件查看器...
    python start_viewer.py
    pause
) else if "%choice%"=="5" (
    exit /b 0
) else (
    echo 无效选择，请重新运行
    pause
)
