import requests
from playwright.sync_api import Playwright, sync_playwright, expect
import time
import imaplib
import email
import re
from email.header import decode_header


def example(playwright: Playwright)-> None:
    try:
        # 直接启动Chrome浏览器并禁用所有插件
            browser = playwright.chromium.launch(
                channel="chrome",
                headless=False,
                args=["--disable-extensions"]  # 禁用所有浏览器插件
            )
            context = browser.new_context()
    except Exception as e:
        print(f"启动Chrome浏览器失败: {e}")
        return None  # 明确返回None避免隐式返回
    # 打开第一个页面并等待用户确认
    #page = browser.new_page()
    page = context.new_page()
    page.goto("https://baidu.com.cn")
    print(page.title())


    



with sync_playwright() as playwright:
        example(playwright)
